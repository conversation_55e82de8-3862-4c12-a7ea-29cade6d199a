/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Cho phép ảnh từ external domains (nếu cần)
    domains: [
      'images.unsplash.com',
      'source.unsplash.com'
    ],
  },

  // C<PERSON><PERSON> hình headers cho cache
  async headers() {
    return [
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
}

module.exports = nextConfig
