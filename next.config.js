/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    // Tối ưu hóa ảnh
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920, 2048, 3840],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
    
    // Cho phép ảnh từ external domains (nếu cần)
    domains: [
      'images.unsplash.com',
      'source.unsplash.com'
    ],
    
    // Cấu hình loader cho ảnh local
    loader: 'default',
    
    // Tối ưu hóa chất lượng ảnh
    quality: 85,
    
    // Minimumcachettl
    minimumCacheTTL: 60,
  },
  
  // Tối ưu hóa build
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['lucide-react'],
  },
  
  // Cấu hình static export nếu cần
  output: 'standalone',
  
  // Cấu hình headers cho cache
  async headers() {
    return [
      {
        source: '/images/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
}

module.exports = nextConfig
