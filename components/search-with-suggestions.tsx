"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Search, Star, TrendingUp } from "lucide-react"
import Link from "next/link"
import Image from "next/image"

const topRatedProducts = [
  {
    id: 1,
    name: "Sữa hạnh nhân premium",
    rating: 4.9,
    price: 85000,
    image: "/placeholder.svg?height=60&width=60",
  },
  { id: 2, name: "Quinoa đỏ", rating: 4.8, price: 120000, image: "/placeholder.svg?height=60&width=60" },
  { id: 3, name: "<PERSON><PERSON><PERSON><PERSON> bò cao cấp", rating: 4.8, price: 195000, image: "/placeholder.svg?height=60&width=60" },
  { id: 4, name: "<PERSON><PERSON><PERSON> m<PERSON><PERSON> c<PERSON> mỏng", rating: 4.7, price: 65000, image: "/placeholder.svg?height=60&width=60" },
  { id: 5, name: "Hạt chia đen", rating: 4.7, price: 75000, image: "/placeholder.svg?height=60&width=60" },
  { id: 6, name: "C<PERSON> hồi t<PERSON><PERSON>i", rating: 4.6, price: 250000, image: "/placeholder.svg?height=60&width=60" },
  { id: 7, name: "Bơ thực vật", rating: 4.6, price: 110000, image: "/placeholder.svg?height=60&width=60" },
  {
    id: 8,
    name: "Granola",
    rating: 4.5,
    price: 85000,
    image: "/placeholder.svg?height=60&width=60",
  },
  { id: 9, name: "Sữa dừa", rating: 4.5, price: 45000, image: "/placeholder.svg?height=60&width=60" },
  { id: 10, name: "Bánh crackers", rating: 4.4, price: 55000, image: "/placeholder.svg?height=60&width=60" },
]

export function SearchWithSuggestions() {
  const [searchTerm, setSearchTerm] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [filteredProducts, setFilteredProducts] = useState(topRatedProducts)
  const searchRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (searchTerm.trim()) {
      const filtered = topRatedProducts.filter((product) =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()),
      )
      setFilteredProducts(filtered)
    } else {
      setFilteredProducts(topRatedProducts)
    }
  }, [searchTerm])

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  const handleSearch = () => {
    if (searchTerm.trim()) {
      window.location.href = `/products?search=${encodeURIComponent(searchTerm)}`
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch()
    }
  }

  return (
    <div className="max-w-2xl mx-auto mb-12" ref={searchRef}>
      <div className="relative">
        <div className="relative h-[50px]">
          <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5 z-10" />
          <Input
            placeholder="Tìm kiếm sản phẩm sạch..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            onFocus={() => setShowSuggestions(true)}
            onKeyPress={handleKeyPress}
            className="pl-12 pr-28 py-4 text-lg bg-white/95 backdrop-blur-sm text-gray-900 border-2 border-white/50 focus:border-green-300 rounded-2xl shadow-lg h-[50px]"
          />
          <Button
            onClick={handleSearch}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-green-600 hover:bg-green-700 rounded-xl px-6 py-2"
          >
            Tìm kiếm
          </Button>
        </div>

        {showSuggestions && (
          <Card className="absolute top-full left-0 right-0 mt-2 z-50 shadow-2xl border-0 bg-white/95 backdrop-blur-sm">
            <CardContent className="p-0">
              <div className="p-4 border-b bg-green-50">
                <div className="flex items-center text-green-700">
                  <TrendingUp className="w-4 h-4 mr-2" />
                  <span className="font-medium text-sm">Sản phẩm được đánh giá cao nhất</span>
                </div>
              </div>
              <div className="max-h-96 overflow-y-auto">
                {filteredProducts.map((product) => (
                  <Link key={product.id} href={`/products/${product.id}`}>
                    <div className="flex items-center p-4 hover:bg-green-50 transition-colors cursor-pointer border-b border-gray-100 last:border-b-0">
                      <Image
                        src={product.image || "/placeholder.svg"}
                        alt={product.name}
                        width={60}
                        height={60}
                        className="rounded-lg object-cover flex-shrink-0"
                      />
                      <div className="ml-4 flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{product.name}</h4>
                        <div className="flex items-center mt-1">
                          <div className="flex items-center">
                            <Star className="w-3 h-3 text-yellow-400 fill-current" />
                            <span className="ml-1 text-sm text-gray-600">{product.rating}</span>
                          </div>
                          <span className="ml-4 text-green-600 font-semibold">
                            {product.price.toLocaleString("vi-VN")}đ
                          </span>
                        </div>
                      </div>
                    </div>
                  </Link>
                ))}
              </div>
              {searchTerm && filteredProducts.length === 0 && (
                <div className="p-8 text-center text-gray-500">
                  <Search className="w-12 h-12 mx-auto mb-4 text-gray-300" />
                  <p>Không tìm thấy sản phẩm nào</p>
                  <p className="text-sm mt-1">Thử tìm kiếm với từ khóa khác</p>
                </div>
              )}
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
