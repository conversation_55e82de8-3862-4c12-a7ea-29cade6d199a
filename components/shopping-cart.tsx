"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { She<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, She<PERSON><PERSON>rigger } from "@/components/ui/sheet"
import { ShoppingCart, Plus, Minus, Trash2, X } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

interface CartItem {
  id: number
  name: string
  price: number
  quantity: number
  image: string
  category: string
}

const initialCartItems: CartItem[] = [
  {
    id: 1,
    name: "Rau cải sạch premium",
    price: 45000,
    quantity: 2,
    image: "https://source.unsplash.com/80x80/?bok-choy,green-vegetable",
    category: "Rau củ quả",
  },
  {
    id: 2,
    name: "Sữa hạnh nhân",
    price: 85000,
    quantity: 1,
    image: "https://source.unsplash.com/80x80/?almond-milk",
    category: "<PERSON><PERSON><PERSON> thực vật",
  },
  {
    id: 3,
    name: "Quinoa đỏ",
    price: 120000,
    quantity: 1,
    image: "https://source.unsplash.com/80x80/?quinoa",
    category: "<PERSON><PERSON> cốc",
  },
]

export function ShoppingCartComponent() {
  const [cartItems, setCartItems] = useState<CartItem[]>(initialCartItems)
  const [isOpen, setIsOpen] = useState(false)

  const updateQuantity = (id: number, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeItem(id)
      return
    }
    setCartItems((items) => items.map((item) => (item.id === id ? { ...item, quantity: newQuantity } : item)))
  }

  const removeItem = (id: number) => {
    setCartItems((items) => items.filter((item) => item.id !== id))
  }

  const getTotalItems = () => {
    return cartItems.reduce((total, item) => total + item.quantity, 0)
  }

  const getTotalPrice = () => {
    return cartItems.reduce((total, item) => total + item.price * item.quantity, 0)
  }

  const clearCart = () => {
    setCartItems([])
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <Button className="bg-green-600 hover:bg-green-700 relative" onClick={() => setIsOpen(true)}>
          <ShoppingCart className="w-4 h-4 mr-2" />
          Giỏ hàng
          {getTotalItems() > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white text-xs min-w-[20px] h-5 flex items-center justify-center rounded-full">
              {getTotalItems()}
            </Badge>
          )}
        </Button>
      </SheetTrigger>

      <SheetContent className="w-full sm:max-w-lg p-5">
        <SheetHeader>
          <SheetTitle className="flex items-center justify-between">
            <span>Giỏ hàng của bạn</span>
            {cartItems.length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearCart}
                className="text-red-500 hover:text-red-700 hover:bg-red-50"
              >
                <Trash2 className="w-4 h-4 mr-1" />
                Xóa tất cả
              </Button>
            )}
          </SheetTitle>
        </SheetHeader>

        <div className="mt-6 flex-1 overflow-y-auto">
          {cartItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center h-64 text-center">
              <ShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Giỏ hàng trống</h3>
              <p className="text-gray-500 mb-6">Thêm sản phẩm để bắt đầu mua sắm</p>
              <Link href="/products">
                <Button className="bg-green-600 hover:bg-green-700" onClick={() => setIsOpen(false)}>
                  Khám phá sản phẩm
                </Button>
              </Link>
            </div>
          ) : (
            <div className="space-y-4">
              {cartItems.map((item) => (
                <Card key={item.id} className="border border-gray-200">
                  <CardContent className="p-4">
                    <div className="flex items-start space-x-4">
                      <Image
                        src={item.image || "/placeholder.svg"}
                        alt={item.name}
                        width={80}
                        height={80}
                        className="rounded-lg object-cover flex-shrink-0"
                      />

                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h4 className="font-medium text-gray-900 line-clamp-2">{item.name}</h4>
                            <Badge variant="outline" className="mt-1 text-xs">
                              {item.category}
                            </Badge>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.id)}
                            className="text-red-500 hover:text-red-700 hover:bg-red-50 p-1"
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>

                        <div className="flex items-center justify-between mt-3">
                          <div className="flex items-center space-x-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="h-8 w-8 p-0"
                            >
                              <Minus className="w-3 h-3" />
                            </Button>
                            <span className="font-medium min-w-[2rem] text-center">{item.quantity}</span>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="h-8 w-8 p-0"
                            >
                              <Plus className="w-3 h-3" />
                            </Button>
                          </div>

                          <div className="text-right">
                            <p className="font-semibold text-green-600">
                              {(item.price * item.quantity).toLocaleString("vi-VN")}đ
                            </p>
                            <p className="text-xs text-gray-500">{item.price.toLocaleString("vi-VN")}đ/sp</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>

        {cartItems.length > 0 && (
          <div className="border-t pt-4 mt-6">
            <div className="space-y-3">
              <div className="flex justify-between text-sm">
                <span>Tạm tính ({getTotalItems()} sản phẩm)</span>
                <span>{getTotalPrice().toLocaleString("vi-VN")}đ</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Phí vận chuyển</span>
                <span className="text-green-600">Miễn phí</span>
              </div>
              <div className="border-t pt-3">
                <div className="flex justify-between font-semibold text-lg">
                  <span>Tổng cộng</span>
                  <span className="text-green-600">{getTotalPrice().toLocaleString("vi-VN")}đ</span>
                </div>
              </div>
            </div>

            <div className="space-y-3 mt-6">
              <Button className="w-full bg-green-600 hover:bg-green-700 py-3">Thanh toán ngay</Button>
              <Button variant="outline" className="w-full bg-transparent" onClick={() => setIsOpen(false)}>
                <Link href="/products" className="w-full">
                  Tiếp tục mua sắm
                </Link>
              </Button>
            </div>
          </div>
        )}
      </SheetContent>
    </Sheet>
  )
}
