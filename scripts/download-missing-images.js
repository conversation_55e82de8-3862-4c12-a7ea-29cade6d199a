const fs = require('fs');
const https = require('https');
const path = require('path');

// Ảnh thay thế cho những ảnh bị lỗi
const missingImages = [
  // Rau củ quả
  { name: 'ca-chua', category: 'vegetables', url: 'https://images.unsplash.com/photo-1592924357228-91a4daadcfea?w=400&h=400&fit=crop' },
  
  // Ngũ cốc
  { name: 'hat-chia', category: 'grains', url: 'https://images.unsplash.com/photo-1609501676725-7186f734b2b0?w=400&h=400&fit=crop' },
  { name: 'hat-lanh', category: 'grains', url: 'https://images.unsplash.com/photo-1609501676725-7186f734b2b0?w=400&h=400&fit=crop' },
  { name: 'granola', category: 'grains', url: 'https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=400&h=400&fit=crop' },
  { name: 'muesli', category: 'grains', url: 'https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=400&h=400&fit=crop' },
  { name: 'banh-mi-ngu-coc', category: 'grains', url: 'https://images.unsplash.com/photo-1586444248902-2f64eddc13df?w=400&h=400&fit=crop' },
  
  // Đậu hạt
  { name: 'hat-oc-cho', category: 'nuts', url: 'https://images.unsplash.com/photo-1448043552756-e747b7a2b2b8?w=400&h=400&fit=crop' },
  { name: 'hat-macca', category: 'nuts', url: 'https://images.unsplash.com/photo-1448043552756-e747b7a2b2b8?w=400&h=400&fit=crop' },
  { name: 'hat-de-cuoi', category: 'nuts', url: 'https://images.unsplash.com/photo-1448043552756-e747b7a2b2b8?w=400&h=400&fit=crop' },
  
  // Thịt, cá, trứng
  { name: 'thit-bo', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=400&h=400&fit=crop' },
  { name: 'thit-heo', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1603048297172-c92544798d5a?w=400&h=400&fit=crop' },
  { name: 'tom-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1565680018434-b513d5573b80?w=400&h=400&fit=crop' },
  { name: 'muc-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1565680018434-b513d5573b80?w=400&h=400&fit=crop' },
  
  // Bánh kẹo
  { name: 'granola-bar', category: 'snacks', url: 'https://images.unsplash.com/photo-1525385133512-2f3bdd039054?w=400&h=400&fit=crop' },
];

// Function download ảnh
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filepath}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Xóa file lỗi
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// Main function
const downloadMissingImages = async () => {
  console.log('Downloading missing images...');
  
  for (const product of missingImages) {
    const filepath = path.join('public', 'images', 'products', product.category, `${product.name}.jpg`);
    
    try {
      await downloadImage(product.url, filepath);
      // Delay để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 200));
    } catch (error) {
      console.error(`Failed to download ${product.name}:`, error.message);
    }
  }
  
  console.log('Missing images download completed!');
};

// Chạy script
downloadMissingImages().catch(console.error);
