const fs = require('fs');
const path = require('path');

// <PERSON><PERSON> sách tất cả sản phẩm cần kiểm tra
const allProducts = [
  // <PERSON>u củ quả
  { name: '<PERSON><PERSON> rốt', file: 'ca-rot', category: 'vegetables' },
  { name: '<PERSON><PERSON> muống', file: 'rau-muong', category: 'vegetables' },
  { name: '<PERSON><PERSON><PERSON> tây', file: 'khoai-tay', category: 'vegetables' },
  { name: 'Bắp cải', file: 'bap-cai', category: 'vegetables' },
  { name: '<PERSON>à chua', file: 'ca-chua', category: 'vegetables' },
  { name: 'Dưa chuột', file: 'dua-chuot', category: 'vegetables' },
  { name: 'Rau cải', file: 'rau-cai', category: 'vegetables' },
  { name: 'Hành tây', file: 'hanh-tay', category: 'vegetables' },
  { name: 'Ớt chuông', file: 'ot-chuong', category: 'vegetables' },
  { name: '<PERSON><PERSON><PERSON> cải xanh', file: 'bong-cai-xanh', category: 'vegetables' },

  // S<PERSON>a thực vật
  { name: 'Sữa hạnh nhân', file: 'sua-hanh-nhan', category: 'plant-milk' },
  { name: 'Sữa yến mạch', file: 'sua-yen-mach', category: 'plant-milk' },
  { name: 'Sữa đậu nành', file: 'sua-dau-nanh', category: 'plant-milk' },
  { name: 'Sữa dừa', file: 'sua-dua', category: 'plant-milk' },
  { name: 'Sữa gạo', file: 'sua-gao', category: 'plant-milk' },
  { name: 'Yogurt dừa', file: 'yogurt-dua', category: 'plant-milk' },
  { name: 'Kem đậu nành', file: 'kem-dau-nanh', category: 'plant-milk' },
  { name: 'Phô mai thực vật', file: 'pho-mai-thuc-vat', category: 'plant-milk' },
  { name: 'Bơ thực vật', file: 'bo-thuc-vat', category: 'plant-milk' },
  { name: 'Sữa chua thực vật', file: 'sua-chua-thuc-vat', category: 'plant-milk' },

  // Ngũ cốc
  { name: 'Quinoa', file: 'quinoa', category: 'grains' },
  { name: 'Yến mạch', file: 'yen-mach', category: 'grains' },
  { name: 'Gạo lứt', file: 'gao-lut', category: 'grains' },
  { name: 'Lúa mì', file: 'lua-mi', category: 'grains' },
  { name: 'Lúa mạch', file: 'lua-mach', category: 'grains' },
  { name: 'Hạt chia', file: 'hat-chia', category: 'grains' },
  { name: 'Hạt lanh', file: 'hat-lanh', category: 'grains' },
  { name: 'Granola', file: 'granola', category: 'grains' },
  { name: 'Muesli', file: 'muesli', category: 'grains' },
  { name: 'Bánh mì ngũ cốc', file: 'banh-mi-ngu-coc', category: 'grains' },

  // Đậu hạt
  { name: 'Đậu đen', file: 'dau-den', category: 'nuts' },
  { name: 'Đậu đỏ', file: 'dau-do', category: 'nuts' },
  { name: 'Đậu xanh', file: 'dau-xanh', category: 'nuts' },
  { name: 'Đậu nành', file: 'dau-nanh', category: 'nuts' },
  { name: 'Đậu phộng', file: 'dau-phong', category: 'nuts' },
  { name: 'Hạt điều', file: 'hat-dieu', category: 'nuts' },
  { name: 'Hạt óc chó', file: 'hat-oc-cho', category: 'nuts' },
  { name: 'Hạt hạnh nhân', file: 'hat-hanh-nhan', category: 'nuts' },
  { name: 'Hạt macca', file: 'hat-macca', category: 'nuts' },
  { name: 'Hạt dẻ cười', file: 'hat-de-cuoi', category: 'nuts' },

  // Thịt, cá, trứng
  { name: 'Thịt bò', file: 'thit-bo', category: 'meat-fish-eggs' },
  { name: 'Thịt heo', file: 'thit-heo', category: 'meat-fish-eggs' },
  { name: 'Thịt gà', file: 'thit-ga', category: 'meat-fish-eggs' },
  { name: 'Cá hồi', file: 'ca-hoi', category: 'meat-fish-eggs' },
  { name: 'Cá thu', file: 'ca-thu', category: 'meat-fish-eggs' },
  { name: 'Cá rô phi', file: 'ca-ro-phi', category: 'meat-fish-eggs' },
  { name: 'Tôm tươi', file: 'tom-tuoi', category: 'meat-fish-eggs' },
  { name: 'Mực tươi', file: 'muc-tuoi', category: 'meat-fish-eggs' },
  { name: 'Trứng gà', file: 'trung-ga', category: 'meat-fish-eggs' },
  { name: 'Trứng vịt', file: 'trung-vit', category: 'meat-fish-eggs' },

  // Gia vị
  { name: 'Muối hồng', file: 'muoi-hong', category: 'spices' },
  { name: 'Tiêu đen', file: 'tieu-den', category: 'spices' },
  { name: 'Nghệ tươi', file: 'nghe-tuoi', category: 'spices' },
  { name: 'Gừng', file: 'gung', category: 'spices' },
  { name: 'Tỏi', file: 'toi', category: 'spices' },
  { name: 'Hành khô', file: 'hanh-kho', category: 'spices' },
  { name: 'Lá nguyệt quế', file: 'la-nguyet-que', category: 'spices' },
  { name: 'Quế', file: 'que', category: 'spices' },
  { name: 'Hồi', file: 'hoi', category: 'spices' },
  { name: 'Đinh hương', file: 'dinh-huong', category: 'spices' },

  // Bánh kẹo
  { name: 'Bánh quy yến mạch', file: 'banh-quy-yen-mach', category: 'snacks' },
  { name: 'Kẹo dẻo', file: 'keo-deo', category: 'snacks' },
  { name: 'Chocolate đen', file: 'chocolate-den', category: 'snacks' },
  { name: 'Bánh mì nướng', file: 'banh-mi-nuong', category: 'snacks' },
  { name: 'Granola bar', file: 'granola-bar', category: 'snacks' },
  { name: 'Bánh crackers', file: 'banh-crackers', category: 'snacks' },
  { name: 'Kẹo gum', file: 'keo-gum', category: 'snacks' },
  { name: 'Bánh wafer', file: 'banh-wafer', category: 'snacks' },
  { name: 'Mứt trái cây', file: 'mut-trai-cay', category: 'snacks' },
  { name: 'Bánh tart', file: 'banh-tart', category: 'snacks' },
];

// Function kiểm tra ảnh
const checkImages = () => {
  console.log('🔍 Checking product images...\n');
  
  let totalImages = 0;
  let existingImages = 0;
  let missingImages = [];
  
  // Kiểm tra từng category
  const categories = ['vegetables', 'plant-milk', 'grains', 'nuts', 'meat-fish-eggs', 'spices', 'snacks'];
  
  categories.forEach(category => {
    console.log(`📁 Category: ${category}`);
    
    const categoryProducts = allProducts.filter(p => p.category === category);
    let categoryExisting = 0;
    
    categoryProducts.forEach(product => {
      totalImages++;
      const imagePath = path.join('public', 'images', 'products', category, `${product.file}.jpg`);
      
      if (fs.existsSync(imagePath)) {
        existingImages++;
        categoryExisting++;
        console.log(`  ✅ ${product.name} (${product.file}.jpg)`);
      } else {
        missingImages.push(product);
        console.log(`  ❌ ${product.name} (${product.file}.jpg) - MISSING`);
      }
    });
    
    console.log(`  📊 ${categoryExisting}/${categoryProducts.length} images found\n`);
  });
  
  // Tổng kết
  console.log('📈 SUMMARY:');
  console.log(`Total products: ${totalImages}`);
  console.log(`Images found: ${existingImages}`);
  console.log(`Images missing: ${missingImages.length}`);
  console.log(`Success rate: ${((existingImages / totalImages) * 100).toFixed(1)}%\n`);
  
  if (missingImages.length > 0) {
    console.log('❌ Missing images:');
    missingImages.forEach(product => {
      console.log(`  - ${product.name} (${product.category}/${product.file}.jpg)`);
    });
  } else {
    console.log('🎉 All images are available!');
  }
  
  // Kiểm tra kích thước file
  console.log('\n📏 Checking file sizes...');
  let totalSize = 0;
  
  allProducts.forEach(product => {
    const imagePath = path.join('public', 'images', 'products', product.category, `${product.file}.jpg`);
    
    if (fs.existsSync(imagePath)) {
      const stats = fs.statSync(imagePath);
      const sizeKB = (stats.size / 1024).toFixed(1);
      totalSize += stats.size;
      
      if (stats.size > 500000) { // > 500KB
        console.log(`  ⚠️  ${product.name}: ${sizeKB}KB (Large file)`);
      }
    }
  });
  
  const totalSizeMB = (totalSize / (1024 * 1024)).toFixed(1);
  console.log(`📦 Total images size: ${totalSizeMB}MB`);
  
  return {
    total: totalImages,
    existing: existingImages,
    missing: missingImages.length,
    successRate: ((existingImages / totalImages) * 100).toFixed(1)
  };
};

// Chạy kiểm tra
const result = checkImages();

// Export kết quả để có thể sử dụng trong script khác
module.exports = { checkImages, allProducts };
