const fs = require('fs');
const https = require('https');
const path = require('path');

// Ảnh cuối cùng cho những sản phẩm còn thiếu
const finalImages = [
  // <PERSON><PERSON> cốc
  { name: 'hat-chia', category: 'grains', url: 'https://images.unsplash.com/photo-1517059224940-d4af9eec41b7?w=400&h=400&fit=crop' },
  { name: 'hat-lanh', category: 'grains', url: 'https://images.unsplash.com/photo-1517059224940-d4af9eec41b7?w=400&h=400&fit=crop' },
  
  // Thịt, cá, trứng
  { name: 'tom-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=400&fit=crop' },
  { name: 'muc-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1559181567-c3190ca9959b?w=400&h=400&fit=crop' },
];

// Function download ảnh
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filepath}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Xóa file lỗi
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// Main function
const downloadFinalImages = async () => {
  console.log('Downloading final images...');
  
  for (const product of finalImages) {
    const filepath = path.join('public', 'images', 'products', product.category, `${product.name}.jpg`);
    
    try {
      await downloadImage(product.url, filepath);
      // Delay để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 300));
    } catch (error) {
      console.error(`Failed to download ${product.name}:`, error.message);
    }
  }
  
  console.log('Final images download completed!');
};

// Chạy script
downloadFinalImages().catch(console.error);
