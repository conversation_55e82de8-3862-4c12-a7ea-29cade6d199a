const fs = require('fs');
const https = require('https');
const path = require('path');

// T<PERSON><PERSON> thư mục nếu chưa có
const createDirectories = () => {
  const dirs = [
    'public/images',
    'public/images/products',
    'public/images/products/vegetables',
    'public/images/products/plant-milk',
    'public/images/products/grains',
    'public/images/products/nuts',
    'public/images/products/meat-fish-eggs',
    'public/images/products/spices',
    'public/images/products/snacks'
  ];

  dirs.forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
      console.log(`Created directory: ${dir}`);
    }
  });
};

// Danh sách sản phẩm cần download ảnh
const products = [
  // Rau củ quả
  { name: 'ca-rot', category: 'vegetables', url: 'https://images.unsplash.com/photo-1445282768818-728615cc910a?w=400&h=400&fit=crop' },
  { name: 'rau-muong', category: 'vegetables', url: 'https://images.unsplash.com/photo-1576045057995-568f588f82fb?w=400&h=400&fit=crop' },
  { name: 'khoai-tay', category: 'vegetables', url: 'https://images.unsplash.com/photo-1518977676601-b53f82aba655?w=400&h=400&fit=crop' },
  { name: 'bap-cai', category: 'vegetables', url: 'https://images.unsplash.com/photo-1594282486552-05b4d80fbb9f?w=400&h=400&fit=crop' },
  { name: 'ca-chua', category: 'vegetables', url: 'https://images.unsplash.com/photo-1546470427-e26264be0b0d?w=400&h=400&fit=crop' },
  { name: 'dua-chuot', category: 'vegetables', url: 'https://images.unsplash.com/photo-1449300079323-02e209d9d3a6?w=400&h=400&fit=crop' },
  { name: 'rau-cai', category: 'vegetables', url: 'https://images.unsplash.com/photo-1515543237350-b3eea1ec8082?w=400&h=400&fit=crop' },
  { name: 'hanh-tay', category: 'vegetables', url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=400&fit=crop' },
  { name: 'ot-chuong', category: 'vegetables', url: 'https://images.unsplash.com/photo-1563565375-f3fdfdbefa83?w=400&h=400&fit=crop' },
  { name: 'bong-cai-xanh', category: 'vegetables', url: 'https://images.unsplash.com/photo-1459411621453-7b03977f4bfc?w=400&h=400&fit=crop' },

  // Sữa thực vật
  { name: 'sua-hanh-nhan', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1563636619-e9143da7973b?w=400&h=400&fit=crop' },
  { name: 'sua-yen-mach', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1550583724-b2692b85b150?w=400&h=400&fit=crop' },
  { name: 'sua-dau-nanh', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1596040033229-a9821ebd058d?w=400&h=400&fit=crop' },
  { name: 'sua-dua', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1481671703460-040cb8a2d909?w=400&h=400&fit=crop' },
  { name: 'sua-gao', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=400&fit=crop' },
  { name: 'yogurt-dua', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1571212515416-fef01fc43637?w=400&h=400&fit=crop' },
  { name: 'kem-dau-nanh', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400&h=400&fit=crop' },
  { name: 'pho-mai-thuc-vat', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1486297678162-eb2a19b0a32d?w=400&h=400&fit=crop' },
  { name: 'bo-thuc-vat', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1589985270826-4b7bb135bc9d?w=400&h=400&fit=crop' },
  { name: 'sua-chua-thuc-vat', category: 'plant-milk', url: 'https://images.unsplash.com/photo-1571212515416-fef01fc43637?w=400&h=400&fit=crop' },

  // Ngũ cốc
  { name: 'quinoa', category: 'grains', url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=400&fit=crop' },
  { name: 'yen-mach', category: 'grains', url: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=400&fit=crop' },
  { name: 'gao-lut', category: 'grains', url: 'https://images.unsplash.com/photo-1586201375761-83865001e31c?w=400&h=400&fit=crop' },
  { name: 'lua-mi', category: 'grains', url: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=400&fit=crop' },
  { name: 'lua-mach', category: 'grains', url: 'https://images.unsplash.com/photo-1574323347407-f5e1ad6d020b?w=400&h=400&fit=crop' },
  { name: 'hat-chia', category: 'grains', url: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=400&fit=crop' },
  { name: 'hat-lanh', category: 'grains', url: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=400&fit=crop' },
  { name: 'granola', category: 'grains', url: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=400&fit=crop' },
  { name: 'muesli', category: 'grains', url: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=400&fit=crop' },
  { name: 'banh-mi-ngu-coc', category: 'grains', url: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=400&fit=crop' },

  // Đậu hạt
  { name: 'dau-den', category: 'nuts', url: 'https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=400&fit=crop' },
  { name: 'dau-do', category: 'nuts', url: 'https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=400&fit=crop' },
  { name: 'dau-xanh', category: 'nuts', url: 'https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=400&fit=crop' },
  { name: 'dau-nanh', category: 'nuts', url: 'https://images.unsplash.com/photo-1583258292688-d0213dc5a3a8?w=400&h=400&fit=crop' },
  { name: 'dau-phong', category: 'nuts', url: 'https://images.unsplash.com/photo-1566478989037-eec170784d0b?w=400&h=400&fit=crop' },
  { name: 'hat-dieu', category: 'nuts', url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=400&fit=crop' },
  { name: 'hat-oc-cho', category: 'nuts', url: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=400&fit=crop' },
  { name: 'hat-hanh-nhan', category: 'nuts', url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=400&fit=crop' },
  { name: 'hat-macca', category: 'nuts', url: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=400&fit=crop' },
  { name: 'hat-de-cuoi', category: 'nuts', url: 'https://images.unsplash.com/photo-1553909489-cd47e0ef937f?w=400&h=400&fit=crop' },

  // Thịt, cá, trứng
  { name: 'thit-bo', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b4?w=400&h=400&fit=crop' },
  { name: 'thit-heo', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1588347818133-38c4106ca7b4?w=400&h=400&fit=crop' },
  { name: 'thit-ga', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1604503468506-a8da13d82791?w=400&h=400&fit=crop' },
  { name: 'ca-hoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=400&fit=crop' },
  { name: 'ca-thu', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=400&fit=crop' },
  { name: 'ca-ro-phi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1544943910-4c1dc44aab44?w=400&h=400&fit=crop' },
  { name: 'tom-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1565680018434-b513d5573b80?w=400&h=400&fit=crop' },
  { name: 'muc-tuoi', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1565680018434-b513d5573b80?w=400&h=400&fit=crop' },
  { name: 'trung-ga', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1518569656558-1f25e69d93d7?w=400&h=400&fit=crop' },
  { name: 'trung-vit', category: 'meat-fish-eggs', url: 'https://images.unsplash.com/photo-1518569656558-1f25e69d93d7?w=400&h=400&fit=crop' },

  // Gia vị
  { name: 'muoi-hong', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },
  { name: 'tieu-den', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },
  { name: 'nghe-tuoi', category: 'spices', url: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=400&h=400&fit=crop' },
  { name: 'gung', category: 'spices', url: 'https://images.unsplash.com/photo-1615485500704-8e990f9900f7?w=400&h=400&fit=crop' },
  { name: 'toi', category: 'spices', url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=400&fit=crop' },
  { name: 'hanh-kho', category: 'spices', url: 'https://images.unsplash.com/photo-1508747703725-719777637510?w=400&h=400&fit=crop' },
  { name: 'la-nguyet-que', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },
  { name: 'que', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },
  { name: 'hoi', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },
  { name: 'dinh-huong', category: 'spices', url: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop' },

  // Bánh kẹo
  { name: 'banh-quy-yen-mach', category: 'snacks', url: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=400&fit=crop' },
  { name: 'keo-deo', category: 'snacks', url: 'https://images.unsplash.com/photo-1582058091505-f87a2e55a40f?w=400&h=400&fit=crop' },
  { name: 'chocolate-den', category: 'snacks', url: 'https://images.unsplash.com/photo-1511381939415-e44015466834?w=400&h=400&fit=crop' },
  { name: 'banh-mi-nuong', category: 'snacks', url: 'https://images.unsplash.com/photo-1509440159596-0249088772ff?w=400&h=400&fit=crop' },
  { name: 'granola-bar', category: 'snacks', url: 'https://images.unsplash.com/photo-1571115764595-644a1f56a55c?w=400&h=400&fit=crop' },
  { name: 'banh-crackers', category: 'snacks', url: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=400&fit=crop' },
  { name: 'keo-gum', category: 'snacks', url: 'https://images.unsplash.com/photo-1582058091505-f87a2e55a40f?w=400&h=400&fit=crop' },
  { name: 'banh-wafer', category: 'snacks', url: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=400&fit=crop' },
  { name: 'mut-trai-cay', category: 'snacks', url: 'https://images.unsplash.com/photo-1571212515416-fef01fc43637?w=400&h=400&fit=crop' },
  { name: 'banh-tart', category: 'snacks', url: 'https://images.unsplash.com/photo-1558961363-fa8fdf82db35?w=400&h=400&fit=crop' },
];

// Function download ảnh
const downloadImage = (url, filepath) => {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filepath);
    
    https.get(url, (response) => {
      if (response.statusCode !== 200) {
        reject(new Error(`Failed to download ${url}: ${response.statusCode}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`Downloaded: ${filepath}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(filepath, () => {}); // Xóa file lỗi
        reject(err);
      });
    }).on('error', (err) => {
      reject(err);
    });
  });
};

// Main function
const downloadAllImages = async () => {
  console.log('Creating directories...');
  createDirectories();
  
  console.log('Starting image downloads...');
  
  for (const product of products) {
    const filepath = path.join('public', 'images', 'products', product.category, `${product.name}.jpg`);
    
    try {
      await downloadImage(product.url, filepath);
      // Delay để tránh rate limit
      await new Promise(resolve => setTimeout(resolve, 100));
    } catch (error) {
      console.error(`Failed to download ${product.name}:`, error.message);
    }
  }
  
  console.log('Download completed!');
};

// Chạy script
downloadAllImages().catch(console.error);
