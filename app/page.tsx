import { Card, CardContent } from "@/components/ui/card"
import { Leaf, Heart, Award } from "lucide-react"
import Link from "next/link"
import { ProductSlider } from "@/components/product-slider"
import { CheapestProductsSlider } from "@/components/cheapest-products-slider"
import { SearchWithSuggestions } from "@/components/search-with-suggestions"
import { ShoppingCartComponent } from "@/components/shopping-cart"

const featuredProducts = [
  {
    id: 1,
    name: "Rau cải sạch",
    price: 25000,
    image: "/images/products/vegetables/rau-cai.jpg",
    rating: 4.8,
    badge: "<PERSON><PERSON> chạy",
  },
  {
    id: 2,
    name: "Sữa hạnh nhân",
    price: 85000,
    image: "/images/products/plant-milk/sua-hanh-nhan.jpg",
    rating: 4.9,
    badge: "<PERSON>ới",
  },
  {
    id: 3,
    name: "Quinoa",
    price: 120000,
    image: "/images/products/grains/quinoa.jpg",
    rating: 4.7,
    badge: "Premium",
  },
]

const categories = [
  { name: "<PERSON>u củ quả", count: 45, icon: "🥬" },
  { name: "<PERSON><PERSON><PERSON> thực vật", count: 28, icon: "🥛" },
  { name: "<PERSON><PERSON> cốc", count: 32, icon: "🌾" },
  { name: "Đậu hạt", count: 25, icon: "🫘" },
  { name: "Thịt, cá, trứng", count: 35, icon: "🥩" },
  { name: "Gia vị", count: 18, icon: "🌿" },
  { name: "Bánh kẹo", count: 22, icon: "🍪" },
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-b from-green-50 to-white">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <svg width="40" height="40" viewBox="0 0 40 40" className="text-green-600">
                <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
                <path d="M12 20c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8z" fill="currentColor" opacity="0.2" />
                <path d="M20 8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2s2-.9 2-2v-8c0-1.1-.9-2-2-2z" fill="currentColor" />
                <path
                  d="M15 15c-.8-.8-.8-2 0-2.8s2-.8 2.8 0l5.7 5.7c.8.8.8 2 0 2.8s-2 .8-2.8 0L15 15z"
                  fill="currentColor"
                />
                <path
                  d="M25 15c.8-.8.8-2 0-2.8s-2-.8-2.8 0l-5.7 5.7c-.8.8.8 2 0 2.8s2 .8 2.8 0L25 15z"
                  fill="currentColor"
                />
              </svg>
              <div>
                <h1 className="text-xl font-bold text-green-800">Anvie</h1>
                <p className="text-xs text-green-600">Thực phẩm sạch Việt Nam</p>
              </div>
            </Link>

            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-green-700 hover:text-green-900 font-medium">
                Trang chủ
              </Link>
              <Link href="/products" className="text-green-700 hover:text-green-900 font-medium">
                Sản phẩm
              </Link>
              <Link href="/categories" className="text-green-700 hover:text-green-900 font-medium">
                Danh mục
              </Link>
              <Link href="/contact" className="text-green-700 hover:text-green-900 font-medium">
                Liên hệ
              </Link>
            </nav>

            <ShoppingCartComponent />
          </div>
        </div>
      </header>

      {/* Hero Section with Search */}
      <section className="bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-6xl font-bold mb-4">Thực Phẩm Sạch</h2>
          <p className="text-xl mb-8 opacity-90">Tươi ngon - Tự nhiên - An toàn cho sức khỏe</p>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-12">
            <SearchWithSuggestions />
          </div>

          {/* Featured Products Slider */}
          <ProductSlider products={featuredProducts} />
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12 text-green-800">Danh Mục Sản Phẩm</h3>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {categories.map((category, index) => (
              <Link key={index} href="/products">
                <Card className="hover:shadow-lg transition-shadow cursor-pointer border-green-200 hover:border-green-400">
                  <CardContent className="p-6 text-center">
                    <div className="text-4xl mb-3">{category.icon}</div>
                    <h4 className="font-semibold text-green-800 mb-1">{category.name}</h4>
                    <p className="text-sm text-green-600">{category.count} sản phẩm</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Cheapest Products Slider */}
      <section className="py-16 bg-green-50">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center mb-12 text-green-800">Sản Phẩm Giá Tốt</h3>
          <CheapestProductsSlider />
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <div className="flex justify-center mb-6">
              <div className="bg-green-100 p-4 rounded-full">
                <Leaf className="w-12 h-12 text-green-600" />
              </div>
            </div>
            <h3 className="text-3xl font-bold mb-6 text-green-800">Tại Sao Chọn Thực Phẩm Sạch?</h3>
            <p className="text-lg text-gray-700 leading-relaxed">
              Thực phẩm sạch không chỉ giúp cải thiện sức khỏe tim mạch, tăng cường hệ miễn dịch mà còn cung cấp
              đầy đủ chất xơ, vitamin và khoáng chất thiết yếu. Việc lựa chọn thực phẩm sạch góp phần bảo vệ môi trường,
              giảm phát thải khí nhà kính và tiết kiệm tài nguyên nước. Hơn nữa, chế độ ăn sạch giúp duy trì cân nặng lý
              tưởng, tăng năng lượng tự nhiên và mang lại cảm giác nhẹ nhàng, thoải mái cho cơ thể.
            </p>
            <div className="grid md:grid-cols-3 gap-8 mt-12">
              <div className="text-center">
                <Heart className="w-8 h-8 text-red-500 mx-auto mb-4" />
                <h4 className="font-semibold text-green-800 mb-2">Tốt Cho Sức Khỏe</h4>
                <p className="text-sm text-gray-600">Giàu vitamin, khoáng chất tự nhiên</p>
              </div>
              <div className="text-center">
                <Leaf className="w-8 h-8 text-green-500 mx-auto mb-4" />
                <h4 className="font-semibold text-green-800 mb-2">Thân Thiện Môi Trường</h4>
                <p className="text-sm text-gray-600">Giảm carbon footprint</p>
              </div>
              <div className="text-center">
                <Award className="w-8 h-8 text-yellow-500 mx-auto mb-4" />
                <h4 className="font-semibold text-green-800 mb-2">Chất Lượng Cao</h4>
                <p className="text-sm text-gray-600">Không hóa chất, thuốc trừ sâu</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-green-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h4 className="font-bold mb-4">Anvie</h4>
              <p className="text-green-200 text-sm">
                Cung cấp thực phẩm hữu cơ chất lượng cao, an toàn cho sức khỏe và môi trường.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Kết</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-green-200 hover:text-white">
                    Trang chủ
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-green-200 hover:text-white">
                    Sản phẩm
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-green-200 hover:text-white">
                    Danh mục
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-green-200 hover:text-white">
                    Liên hệ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Danh Mục</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/categories/rau-cu-qua" className="text-green-200 hover:text-white">
                    Rau củ quả
                  </Link>
                </li>
                <li>
                  <Link href="/categories/sua-thuc-vat" className="text-green-200 hover:text-white">
                    Sữa thực vật
                  </Link>
                </li>
                <li>
                  <Link href="/categories/ngu-coc" className="text-green-200 hover:text-white">
                    Ngũ cốc
                  </Link>
                </li>
                <li>
                  <Link href="/categories/dau-hat" className="text-green-200 hover:text-white">
                    Đậu hạt
                  </Link>
                </li>
                <li>
                  <Link href="/categories/thit-ca-trung" className="text-green-200 hover:text-white">
                    Thịt, cá, trứng
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Hệ</h4>
              <p className="text-green-200 text-sm mb-2">📧 <EMAIL></p>
              <p className="text-green-200 text-sm mb-2">📞 0123 456 789</p>
              <p className="text-green-200 text-sm">📍 TP. Hồ Chí Minh</p>
            </div>
          </div>
          <div className="border-t border-green-700 mt-8 pt-8 text-center">
            <p className="text-green-200 text-sm">© 2025 Anvie. Tất cả quyền được bảo lưu.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
