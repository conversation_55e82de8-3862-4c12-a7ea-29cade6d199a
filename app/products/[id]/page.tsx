"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Star, ShoppingCart, Heart, Share2, Minus, Plus, Truck, Shield, Leaf, Award } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { ShoppingCartComponent } from "@/components/shopping-cart"

// Mock product data - in real app, this would come from API
const getProductById = (id: string) => {
  return {
    id: Number.parseInt(id),
    name: `Rau cải sạch premium ${id}`,
    price: 45000,
    originalPrice: 55000,
    category: "Rau củ quả",
    rating: 4.8,
    reviewCount: 124,
    images: [
      `/placeholder.svg?height=500&width=500&query=organic+vegetables+${id}`,
      `/placeholder.svg?height=500&width=500&query=fresh+organic+greens`,
      `/placeholder.svg?height=500&width=500&query=organic+farming`,
      `/placeholder.svg?height=500&width=500&query=healthy+vegetables`,
    ],
    description:
      "Rau cải sạch được trồng theo phương pháp tự nhiên, không sử dụng thuốc trừ sâu hay phân bón hóa học. Giàu vitamin A, C, K và các khoáng chất thiết yếu cho cơ thể.",
    features: [
      "100% sạch, không hóa chất",
      "Giàu vitamin và khoáng chất",
      "Trồng theo phương pháp bền vững",
      "Tươi ngon, thu hoạch trong ngày",
      "Đóng gói an toàn, vệ sinh",
    ],
    nutritionFacts: {
      calories: "25 kcal/100g",
      protein: "2.9g",
      carbs: "4.6g",
      fiber: "2.6g",
      vitaminC: "120mg",
      calcium: "105mg",
    },
    inStock: true,
    stockQuantity: 50,
    origin: "Đà Lạt, Việt Nam",
    certifications: ["Organic", "VietGAP", "Non-GMO"],
    relatedProducts: [
      { id: 2, name: "Cà rốt", price: 35000, image: "/placeholder.svg?height=200&width=200" },
      { id: 3, name: "Bông cải xanh", price: 55000, image: "/placeholder.svg?height=200&width=200" },
      { id: 4, name: "Cà chua cherry", price: 65000, image: "/placeholder.svg?height=200&width=200" },
    ],
  }
}

export default function ProductDetailPage({ params }: { params: { id: string } }) {
  const product = getProductById(params.id)
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isFavorite, setIsFavorite] = useState(false)

  const handleQuantityChange = (change: number) => {
    setQuantity((prev) => Math.max(1, Math.min(prev + change, product.stockQuantity)))
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <svg width="40" height="40" viewBox="0 0 40 40" className="text-green-600">
                <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
                <path d="M12 20c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8z" fill="currentColor" opacity="0.2" />
                <path d="M20 8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2s2-.9 2-2v-8c0-1.1-.9-2-2-2z" fill="currentColor" />
                <path
                  d="M15 15c-.8-.8-.8-2 0-2.8s2-.8 2.8 0l5.7 5.7c.8.8.8 2 0 2.8s-2 .8-2.8 0L15 15z"
                  fill="currentColor"
                />
                <path
                  d="M25 15c.8-.8.8-2 0-2.8s-2-.8-2.8 0l-5.7 5.7c-.8.8.8 2 0 2.8s2 .8 2.8 0L25 15z"
                  fill="currentColor"
                />
              </svg>
              <div>
                <h1 className="text-xl font-bold text-green-800">Anvie</h1>
                <p className="text-xs text-green-600">Thực phẩm sạch Việt Nam</p>
              </div>
            </Link>

            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-green-700 hover:text-green-900 font-medium">
                Trang chủ
              </Link>
              <Link href="/products" className="text-green-700 hover:text-green-900 font-medium">
                Sản phẩm
              </Link>
              <Link href="/categories" className="text-green-700 hover:text-green-900 font-medium">
                Danh mục
              </Link>
              <Link href="/contact" className="text-green-700 hover:text-green-900 font-medium">
                Liên hệ
              </Link>
            </nav>

            <ShoppingCartComponent />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm">
          <Link href="/" className="text-green-600 hover:text-green-800">
            Trang chủ
          </Link>
          <span className="mx-2 text-gray-400">/</span>
          <Link href="/products" className="text-green-600 hover:text-green-800">
            Sản phẩm
          </Link>
          <span className="mx-2 text-gray-400">/</span>
          <span className="text-gray-600">{product.name}</span>
        </nav>

        <div className="grid lg:grid-cols-2 gap-12 mb-12">
          {/* Product Images */}
          <div>
            <div className="mb-4">
              <Image
                src={product.images[selectedImage] || "/placeholder.svg"}
                alt={product.name}
                width={500}
                height={500}
                className="rounded-lg object-cover w-full h-96"
              />
            </div>
            <div className="grid grid-cols-4 gap-2">
              {product.images.map((image, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`border-2 rounded-lg overflow-hidden ${
                    selectedImage === index ? "border-green-500" : "border-gray-200"
                  }`}
                >
                  <Image
                    src={image || "/placeholder.svg"}
                    alt={`${product.name} ${index + 1}`}
                    width={100}
                    height={100}
                    className="object-cover w-full h-20"
                  />
                </button>
              ))}
            </div>
          </div>

          {/* Product Info */}
          <div>
            <div className="mb-4">
              <Badge className="bg-green-100 text-green-800 mb-2">{product.category}</Badge>
              <h1 className="text-3xl font-bold text-green-800 mb-2">{product.name}</h1>
              <div className="flex items-center mb-4">
                <div className="flex items-center mr-4">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < Math.floor(product.rating) ? "text-yellow-400 fill-current" : "text-gray-300"
                      }`}
                    />
                  ))}
                  <span className="ml-2 text-sm text-gray-600">
                    {product.rating} ({product.reviewCount} đánh giá)
                  </span>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <div className="flex items-center space-x-4 mb-4">
                <span className="text-3xl font-bold text-green-600">{product.price.toLocaleString("vi-VN")}đ</span>
                {product.originalPrice && (
                  <span className="text-xl text-gray-500 line-through">
                    {product.originalPrice.toLocaleString("vi-VN")}đ
                  </span>
                )}
              </div>

              <div className="flex items-center space-x-2 mb-4">
                {product.certifications.map((cert, index) => (
                  <Badge key={index} variant="outline" className="text-green-700 border-green-300">
                    {cert}
                  </Badge>
                ))}
              </div>
            </div>

            <div className="mb-6">
              <p className="text-gray-700 leading-relaxed">{product.description}</p>
            </div>

            <div className="mb-6">
              <h3 className="font-semibold text-green-800 mb-3">Đặc điểm nổi bật:</h3>
              <ul className="space-y-2">
                {product.features.map((feature, index) => (
                  <li key={index} className="flex items-center">
                    <Leaf className="w-4 h-4 text-green-500 mr-2 flex-shrink-0" />
                    <span className="text-sm text-gray-700">{feature}</span>
                  </li>
                ))}
              </ul>
            </div>

            {/* Quantity and Add to Cart */}
            <div className="mb-6">
              <div className="flex items-center space-x-4 mb-4">
                <span className="font-medium">Số lượng:</span>
                <div className="flex items-center border border-gray-300 rounded-lg">
                  <Button variant="ghost" size="sm" onClick={() => handleQuantityChange(-1)} disabled={quantity <= 1}>
                    <Minus className="w-4 h-4" />
                  </Button>
                  <span className="px-4 py-2 font-medium">{quantity}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleQuantityChange(1)}
                    disabled={quantity >= product.stockQuantity}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
                <span className="text-sm text-gray-600">({product.stockQuantity} sản phẩm có sẵn)</span>
              </div>

              <div className="flex space-x-4">
                <Button className="flex-1 bg-green-600 hover:bg-green-700">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Thêm vào giỏ hàng
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setIsFavorite(!isFavorite)}
                  className={isFavorite ? "text-red-500 border-red-300" : ""}
                >
                  <Heart className={`w-4 h-4 ${isFavorite ? "fill-current" : ""}`} />
                </Button>
                <Button variant="outline">
                  <Share2 className="w-4 h-4" />
                </Button>
              </div>
            </div>

            {/* Shipping Info */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-green-50 rounded-lg">
              <div className="flex items-center">
                <Truck className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Giao hàng nhanh</p>
                  <p className="text-xs text-gray-600">Trong 24h</p>
                </div>
              </div>
              <div className="flex items-center">
                <Shield className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Đảm bảo chất lượng</p>
                  <p className="text-xs text-gray-600">Hoàn tiền 100%</p>
                </div>
              </div>
              <div className="flex items-center">
                <Award className="w-5 h-5 text-green-600 mr-2" />
                <div>
                  <p className="text-sm font-medium">Chứng nhận hữu cơ</p>
                  <p className="text-xs text-gray-600">Quốc tế</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <Tabs defaultValue="description" className="mb-12">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="description">Mô tả chi tiết</TabsTrigger>
            <TabsTrigger value="nutrition">Thông tin dinh dưỡng</TabsTrigger>
            <TabsTrigger value="reviews">Đánh giá</TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-green-800 mb-4">Thông tin sản phẩm</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <p className="text-gray-700 mb-4">{product.description}</p>
                    <h4 className="font-semibold mb-2">Xuất xứ:</h4>
                    <p className="text-gray-700 mb-4">{product.origin}</p>
                    <h4 className="font-semibold mb-2">Bảo quản:</h4>
                    <p className="text-gray-700">
                      Bảo quản trong ngăn mát tủ lạnh, sử dụng trong vòng 3-5 ngày sau khi mua.
                    </p>
                  </div>
                  <div>
                    <h4 className="font-semibold mb-2">Cách sử dụng:</h4>
                    <ul className="text-gray-700 space-y-1 mb-4">
                      <li>• Rửa sạch trước khi sử dụng</li>
                      <li>• Có thể ăn sống hoặc nấu chín</li>
                      <li>• Thích hợp cho salad, xào, luộc</li>
                      <li>• Kết hợp tốt với các loại rau khác</li>
                    </ul>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="nutrition" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-green-800 mb-4">Thành phần dinh dưỡng (100g)</h3>
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="space-y-3">
                    <div className="flex justify-between border-b pb-2">
                      <span>Calories</span>
                      <span className="font-medium">{product.nutritionFacts.calories}</span>
                    </div>
                    <div className="flex justify-between border-b pb-2">
                      <span>Protein</span>
                      <span className="font-medium">{product.nutritionFacts.protein}</span>
                    </div>
                    <div className="flex justify-between border-b pb-2">
                      <span>Carbohydrate</span>
                      <span className="font-medium">{product.nutritionFacts.carbs}</span>
                    </div>
                  </div>
                  <div className="space-y-3">
                    <div className="flex justify-between border-b pb-2">
                      <span>Chất xơ</span>
                      <span className="font-medium">{product.nutritionFacts.fiber}</span>
                    </div>
                    <div className="flex justify-between border-b pb-2">
                      <span>Vitamin C</span>
                      <span className="font-medium">{product.nutritionFacts.vitaminC}</span>
                    </div>
                    <div className="flex justify-between border-b pb-2">
                      <span>Calcium</span>
                      <span className="font-medium">{product.nutritionFacts.calcium}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="reviews" className="mt-6">
            <Card>
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-green-800 mb-4">Đánh giá khách hàng</h3>
                <div className="space-y-4">
                  <div className="border-b pb-4">
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-4">
                        {[...Array(5)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                      </div>
                      <span className="font-medium">Nguyễn Văn A</span>
                      <span className="text-sm text-gray-500 ml-2">2 ngày trước</span>
                    </div>
                    <p className="text-gray-700">Rau rất tươi và ngon, đóng gói cẩn thận. Sẽ mua lại!</p>
                  </div>
                  <div className="border-b pb-4">
                    <div className="flex items-center mb-2">
                      <div className="flex items-center mr-4">
                        {[...Array(4)].map((_, i) => (
                          <Star key={i} className="w-4 h-4 text-yellow-400 fill-current" />
                        ))}
                        <Star className="w-4 h-4 text-gray-300" />
                      </div>
                      <span className="font-medium">Trần Thị B</span>
                      <span className="text-sm text-gray-500 ml-2">1 tuần trước</span>
                    </div>
                    <p className="text-gray-700">Chất lượng tốt, giá hợp lý. Giao hàng nhanh.</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Related Products */}
        <div>
          <h3 className="text-2xl font-bold text-green-800 mb-6">Sản phẩm liên quan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {product.relatedProducts.map((relatedProduct) => (
              <Link key={relatedProduct.id} href={`/products/${relatedProduct.id}`}>
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-4">
                    <Image
                      src={relatedProduct.image || "/placeholder.svg"}
                      alt={relatedProduct.name}
                      width={200}
                      height={200}
                      className="rounded-lg object-cover w-full h-48 mb-3"
                    />
                    <h4 className="font-semibold text-green-800 mb-2">{relatedProduct.name}</h4>
                    <p className="text-lg font-bold text-green-600">{relatedProduct.price.toLocaleString("vi-VN")}đ</p>
                  </CardContent>
                </Card>
              </Link>
            ))}
          </div>
        </div>
      </div>
      {/* Footer */}
      <footer className="bg-green-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h4 className="font-bold mb-4">Anvie</h4>
              <p className="text-green-200 text-sm">
                Cung cấp thực phẩm sạch chất lượng cao, an toàn cho sức khỏe và môi trường.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Kết</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-green-200 hover:text-white">
                    Trang chủ
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-green-200 hover:text-white">
                    Sản phẩm
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-green-200 hover:text-white">
                    Danh mục
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-green-200 hover:text-white">
                    Liên hệ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Danh Mục</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/categories/rau-cu-qua" className="text-green-200 hover:text-white">
                    Rau củ quả
                  </Link>
                </li>
                <li>
                  <Link href="/categories/sua-thuc-vat" className="text-green-200 hover:text-white">
                    Sữa thực vật
                  </Link>
                </li>
                <li>
                  <Link href="/categories/ngu-coc" className="text-green-200 hover:text-white">
                    Ngũ cốc
                  </Link>
                </li>
                <li>
                  <Link href="/categories/dau-hat" className="text-green-200 hover:text-white">
                    Đậu hạt
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Hệ</h4>
              <p className="text-green-200 text-sm mb-2">📧 <EMAIL></p>
              <p className="text-green-200 text-sm mb-2">📞 0123 456 789</p>
              <p className="text-green-200 text-sm">📍 TP. Hồ Chí Minh</p>
            </div>
          </div>
          <div className="border-t border-green-700 mt-8 pt-8 text-center">
            <p className="text-green-200 text-sm">© 2025 Anvie. Tất cả quyền được bảo lưu.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
