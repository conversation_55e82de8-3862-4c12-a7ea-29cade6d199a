"use client"

import { useState, useMemo } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Filter, Star, ChevronLeft, ChevronRight, ArrowLeft } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { ShoppingCartComponent } from "@/components/shopping-cart"

const categoryData = {
  "rau-cu-qua": {
    name: "<PERSON>u củ quả",
    icon: "🥬",
    description: "<PERSON>u củ quả tươi ngon, sạch, kh<PERSON><PERSON> hóa chất, giàu vitamin và khoáng chất thiết yếu",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "sua-thuc-vat": {
    name: "<PERSON><PERSON><PERSON> thực vật",
    icon: "🥛",
    description: "<PERSON><PERSON><PERSON> từ hạt và thực vật, kh<PERSON><PERSON> lactose, gi<PERSON><PERSON> dinh dưỡng, thân thiện với môi trường",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "ngu-coc": {
    name: "Ngũ cốc",
    icon: "🌾",
    description: "Ngũ cốc nguyên hạt, giàu chất xơ và protein, cung cấp năng lượng bền vững",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "thit-ca-trung": {
    name: "Thịt, cá, trứng",
    icon: "🥩",
    description: "Thịt, cá, trứng tươi ngon, chất lượng cao, nguồn protein động vật thiết yếu",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "dau-hat": {
    name: "Đậu hạt",
    icon: "🫘",
    description: "Đậu hạt dinh dưỡng, nguồn protein thực vật tuyệt vời, giàu chất xơ",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "gia-vi": {
    name: "Gia vị",
    icon: "🌿",
    description: "Gia vị tự nhiên, thảo mộc sạch, tăng hương vị và giá trị dinh dưỡng",
    image: "/placeholder.svg?height=400&width=1200",
  },
  "banh-keo": {
    name: "Bánh kẹo",
    icon: "🍪",
    description: "Bánh kẹo ngon miệng, không chất bảo quản, làm từ nguyên liệu tự nhiên",
    image: "/placeholder.svg?height=400&width=1200",
  },
}

// Helper function để lấy ảnh local cho sản phẩm
const getProductImage = (productName: string) => {
  // Map tên sản phẩm tiếng Việt sang tên file và category
  const imageMap: { [key: string]: { file: string, category: string } } = {
    // Rau củ quả
    "Cà rốt": { file: "ca-rot", category: "vegetables" },
    "Rau muống": { file: "rau-muong", category: "vegetables" },
    "Khoai tây": { file: "khoai-tay", category: "vegetables" },
    "Bắp cải": { file: "bap-cai", category: "vegetables" },
    "Cà chua": { file: "ca-chua", category: "vegetables" },
    "Dưa chuột": { file: "dua-chuot", category: "vegetables" },
    "Rau cải": { file: "rau-cai", category: "vegetables" },
    "Hành tây": { file: "hanh-tay", category: "vegetables" },
    "Ớt chuông": { file: "ot-chuong", category: "vegetables" },
    "Bông cải xanh": { file: "bong-cai-xanh", category: "vegetables" },

    // Sữa thực vật
    "Sữa hạnh nhân": { file: "sua-hanh-nhan", category: "plant-milk" },
    "Sữa yến mạch": { file: "sua-yen-mach", category: "plant-milk" },
    "Sữa đậu nành": { file: "sua-dau-nanh", category: "plant-milk" },
    "Sữa dừa": { file: "sua-dua", category: "plant-milk" },
    "Sữa gạo": { file: "sua-gao", category: "plant-milk" },
    "Yogurt dừa": { file: "yogurt-dua", category: "plant-milk" },
    "Kem đậu nành": { file: "kem-dau-nanh", category: "plant-milk" },
    "Phô mai thực vật": { file: "pho-mai-thuc-vat", category: "plant-milk" },
    "Bơ thực vật": { file: "bo-thuc-vat", category: "plant-milk" },
    "Sữa chua thực vật": { file: "sua-chua-thuc-vat", category: "plant-milk" },

    // Ngũ cốc
    "Quinoa": { file: "quinoa", category: "grains" },
    "Yến mạch": { file: "yen-mach", category: "grains" },
    "Gạo lứt": { file: "gao-lut", category: "grains" },
    "Lúa mì": { file: "lua-mi", category: "grains" },
    "Lúa mạch": { file: "lua-mach", category: "grains" },
    "Hạt chia": { file: "hat-chia", category: "grains" },
    "Hạt lanh": { file: "hat-lanh", category: "grains" },
    "Granola": { file: "granola", category: "grains" },
    "Muesli": { file: "muesli", category: "grains" },
    "Bánh mì ngũ cốc": { file: "banh-mi-ngu-coc", category: "grains" },

    // Đậu hạt
    "Đậu đen": { file: "dau-den", category: "nuts" },
    "Đậu đỏ": { file: "dau-do", category: "nuts" },
    "Đậu xanh": { file: "dau-xanh", category: "nuts" },
    "Đậu nành": { file: "dau-nanh", category: "nuts" },
    "Đậu phộng": { file: "dau-phong", category: "nuts" },
    "Hạt điều": { file: "hat-dieu", category: "nuts" },
    "Hạt óc chó": { file: "hat-oc-cho", category: "nuts" },
    "Hạt hạnh nhân": { file: "hat-hanh-nhan", category: "nuts" },
    "Hạt macca": { file: "hat-macca", category: "nuts" },
    "Hạt dẻ cười": { file: "hat-de-cuoi", category: "nuts" },

    // Thịt, cá, trứng
    "Thịt bò": { file: "thit-bo", category: "meat-fish-eggs" },
    "Thịt heo": { file: "thit-heo", category: "meat-fish-eggs" },
    "Thịt gà": { file: "thit-ga", category: "meat-fish-eggs" },
    "Cá hồi": { file: "ca-hoi", category: "meat-fish-eggs" },
    "Cá thu": { file: "ca-thu", category: "meat-fish-eggs" },
    "Cá rô phi": { file: "ca-ro-phi", category: "meat-fish-eggs" },
    "Tôm tươi": { file: "tom-tuoi", category: "meat-fish-eggs" },
    "Mực tươi": { file: "muc-tuoi", category: "meat-fish-eggs" },
    "Trứng gà": { file: "trung-ga", category: "meat-fish-eggs" },
    "Trứng vịt": { file: "trung-vit", category: "meat-fish-eggs" },

    // Gia vị
    "Muối hồng": { file: "muoi-hong", category: "spices" },
    "Tiêu đen": { file: "tieu-den", category: "spices" },
    "Nghệ tươi": { file: "nghe-tuoi", category: "spices" },
    "Gừng": { file: "gung", category: "spices" },
    "Tỏi": { file: "toi", category: "spices" },
    "Hành khô": { file: "hanh-kho", category: "spices" },
    "Lá nguyệt quế": { file: "la-nguyet-que", category: "spices" },
    "Quế": { file: "que", category: "spices" },
    "Hồi": { file: "hoi", category: "spices" },
    "Đinh hương": { file: "dinh-huong", category: "spices" },

    // Bánh kẹo
    "Bánh quy yến mạch": { file: "banh-quy-yen-mach", category: "snacks" },
    "Kẹo dẻo": { file: "keo-deo", category: "snacks" },
    "Chocolate đen": { file: "chocolate-den", category: "snacks" },
    "Bánh mì nướng": { file: "banh-mi-nuong", category: "snacks" },
    "Granola bar": { file: "granola-bar", category: "snacks" },
    "Bánh crackers": { file: "banh-crackers", category: "snacks" },
    "Kẹo gum": { file: "keo-gum", category: "snacks" },
    "Bánh wafer": { file: "banh-wafer", category: "snacks" },
    "Mứt trái cây": { file: "mut-trai-cay", category: "snacks" },
    "Bánh tart": { file: "banh-tart", category: "snacks" },
  }

  // Lấy tên cơ bản của sản phẩm (loại bỏ các từ như premium, tươi, etc.)
  const baseName = productName.split(' ')[0] + (productName.split(' ')[1] && !['premium', 'tươi', 'cao', 'đặc', 'ngon', 'chất', 'tự', 'sạch', 'nhỏ', 'vừa', 'lớn', 'jumbo', 'Đà', 'Việt', 'nhập', 'địa'].includes(productName.split(' ')[1]) ? ' ' + productName.split(' ')[1] : '')

  const imageInfo = imageMap[baseName]

  if (imageInfo) {
    return `/images/products/${imageInfo.category}/${imageInfo.file}.jpg`
  }

  // Fallback về placeholder nếu không tìm thấy
  return `/placeholder.svg?height=250&width=250&text=${encodeURIComponent(productName)}`
}

// Generate products for specific category
const generateCategoryProducts = (categorySlug: string) => {
  const categoryName = categoryData[categorySlug as keyof typeof categoryData]?.name || "Sản phẩm"
  const products = []

  const productsByCategory = {
    "rau-cu-qua": [
      "Cà rốt",
      "Rau muống",
      "Khoai tây",
      "Bắp cải",
      "Cà chua",
      "Dưa chuột",
      "Rau cải",
      "Hành tây",
      "Ớt chuông",
      "Bông cải xanh",
    ],
    "sua-thuc-vat": [
      "Sữa hạnh nhân",
      "Sữa yến mạch",
      "Sữa đậu nành",
      "Sữa dừa",
      "Sữa gạo",
      "Yogurt dừa",
      "Kem đậu nành",
      "Phô mai thực vật",
      "Bơ thực vật",
      "Sữa chua thực vật",
    ],
    "ngu-coc": [
      "Quinoa",
      "Yến mạch",
      "Gạo lứt",
      "Lúa mì",
      "Lúa mạch",
      "Hạt chia",
      "Hạt lanh",
      "Granola",
      "Muesli",
      "Bánh mì ngũ cốc",
    ],
    "dau-hat": [
      "Đậu đen",
      "Đậu đỏ",
      "Đậu xanh",
      "Đậu nành",
      "Đậu phộng",
      "Hạt điều",
      "Hạt óc chó",
      "Hạt hạnh nhân",
      "Hạt macca",
      "Hạt dẻ cười",
    ],
    "gia-vi": [
      "Muối hồng",
      "Tiêu đen",
      "Nghệ tươi",
      "Gừng",
      "Tỏi",
      "Hành khô",
      "Lá nguyệt quế",
      "Quế",
      "Hồi",
      "Đinh hương",
    ],
    "thit-ca-trung": [
      "Thịt bò",
      "Thịt heo",
      "Thịt gà",
      "Cá hồi",
      "Cá thu",
      "Cá rô phi",
      "Tôm tươi",
      "Mực tươi",
      "Trứng gà",
      "Trứng vịt",
    ],
    "banh-keo": [
      "Bánh quy yến mạch",
      "Kẹo dẻo",
      "Chocolate đen",
      "Bánh mì nướng",
      "Granola bar",
      "Bánh crackers",
      "Kẹo gum",
      "Bánh wafer",
      "Mứt trái cây",
      "Bánh tart",
    ],
  }

  const categoryProducts = productsByCategory[categorySlug as keyof typeof productsByCategory] || []
  const usedNames = new Set()

  // Các từ bổ sung để tạo biến thể
  const qualifiers = ["premium", "tươi", "cao cấp", "đặc biệt", "ngon", "chất lượng", "tự nhiên", "sạch"]
  const sizes = ["nhỏ", "vừa", "lớn", "jumbo"]
  const origins = ["Đà Lạt", "Việt Nam", "nhập khẩu", "địa phương"]

  for (let i = 1; i <= 30; i++) {
    let productName
    let finalName
    let attempts = 0

    // Tìm tên sản phẩm chưa được sử dụng
    do {
      productName = categoryProducts[Math.floor(Math.random() * categoryProducts.length)]

      // Tạo biến thể của tên sản phẩm
      if (attempts < 10) {
        finalName = productName
      } else if (attempts < 20) {
        const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)]
        finalName = `${productName} ${qualifier}`
      } else if (attempts < 30) {
        const size = sizes[Math.floor(Math.random() * sizes.length)]
        finalName = `${productName} ${size}`
      } else if (attempts < 40) {
        const origin = origins[Math.floor(Math.random() * origins.length)]
        finalName = `${productName} ${origin}`
      } else {
        // Cuối cùng thêm số ngẫu nhiên
        finalName = `${productName} ${Math.floor(Math.random() * 1000) + 1}`
      }

      attempts++

      // Tránh vòng lặp vô hạn
      if (attempts > 50) {
        finalName = `${productName} ${Date.now()}`
        break
      }
    } while (usedNames.has(finalName))

    usedNames.add(finalName)

    const price = Math.floor(Math.random() * 200000) + 10000
    const rating = (Math.random() * 2 + 3).toFixed(1)

    products.push({
      id: i,
      name: finalName,
      price,
      category: categoryName,
      rating: Number.parseFloat(rating),
      image: getProductImage(finalName),
      description: `${finalName} sạch chất lượng cao, không hóa chất`,
      inStock: Math.random() > 0.1,
    })
  }
  return products
}

export default function CategoryPage({ params }: { params: { category: string } }) {
  const categoryInfo = categoryData[params.category as keyof typeof categoryData]
  const [searchTerm, setSearchTerm] = useState("")
  const [priceFilter, setPriceFilter] = useState("all")
  const [sortBy, setSortBy] = useState("name")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 20

  const allProducts = generateCategoryProducts(params.category)

  const filteredAndSortedProducts = useMemo(() => {
    let filtered = allProducts.filter((product) => product.name.toLowerCase().includes(searchTerm.toLowerCase()))

    // Price filter
    if (priceFilter !== "all") {
      switch (priceFilter) {
        case "under50k":
          filtered = filtered.filter((p) => p.price < 50000)
          break
        case "50k-100k":
          filtered = filtered.filter((p) => p.price >= 50000 && p.price < 100000)
          break
        case "over100k":
          filtered = filtered.filter((p) => p.price >= 100000)
          break
      }
    }

    // Sort
    filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.name.localeCompare(b.name)
        case "price-low":
          return a.price - b.price
        case "price-high":
          return b.price - a.price
        case "rating":
          return b.rating - a.rating
        default:
          return 0
      }
    })

    return filtered
  }, [searchTerm, priceFilter, sortBy, allProducts])

  const totalPages = Math.ceil(filteredAndSortedProducts.length / itemsPerPage)
  const currentProducts = filteredAndSortedProducts.slice((currentPage - 1) * itemsPerPage, currentPage * itemsPerPage)

  if (!categoryInfo) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-800 mb-4">Danh mục không tồn tại</h1>
          <Link href="/categories">
            <Button className="bg-green-600 hover:bg-green-700">Quay lại danh mục</Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <svg width="40" height="40" viewBox="0 0 40 40" className="text-green-600">
                <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
                <path d="M12 20c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8z" fill="currentColor" opacity="0.2" />
                <path d="M20 8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2s2-.9 2-2v-8c0-1.1-.9-2-2-2z" fill="currentColor" />
                <path
                  d="M15 15c-.8-.8-.8-2 0-2.8s2-.8 2.8 0l5.7 5.7c.8.8.8 2 0 2.8s-2 .8-2.8 0L15 15z"
                  fill="currentColor"
                />
                <path
                  d="M25 15c.8-.8.8-2 0-2.8s-2-.8-2.8 0l-5.7 5.7c-.8.8.8 2 0 2.8s2 .8 2.8 0L25 15z"
                  fill="currentColor"
                />
              </svg>
              <div>
                <h1 className="text-xl font-bold text-green-800">Anvie</h1>
                <p className="text-xs text-green-600">Thực phẩm sạch Việt Nam</p>
              </div>
            </Link>

            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-green-700 hover:text-green-900 font-medium">
                Trang chủ
              </Link>
              <Link href="/products" className="text-green-700 hover:text-green-900 font-medium">
                Sản phẩm
              </Link>
              <Link href="/categories" className="text-green-700 hover:text-green-900 font-medium">
                Danh mục
              </Link>
              <Link href="/contact" className="text-green-700 hover:text-green-900 font-medium">
                Liên hệ
              </Link>
            </nav>

            <ShoppingCartComponent />
          </div>
        </div>
      </header>

      {/* Category Hero */}
      <div className="relative bg-gradient-to-r from-green-600 to-green-700 text-white py-16">
        <div className="absolute inset-0 opacity-20">
          <img
            src={categoryInfo.image || "/placeholder.svg"}
            alt={categoryInfo.name}
            className="w-full h-full object-cover"
          />
        </div>
        <div className="relative container mx-auto px-4">
          <div className="flex items-center mb-4">
            <Link href="/categories" className="flex items-center text-white/80 hover:text-white mr-4">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Quay lại danh mục
            </Link>
          </div>
          <div className="flex items-center mb-4">
            <span className="text-6xl mr-4">{categoryInfo.icon}</span>
            <div>
              <h1 className="text-4xl font-bold mb-2">{categoryInfo.name}</h1>
              <p className="text-xl opacity-90">{categoryInfo.description}</p>
            </div>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb */}
        <nav className="mb-6 text-sm">
          <Link href="/" className="text-green-600 hover:text-green-800">
            Trang chủ
          </Link>
          <span className="mx-2 text-gray-400">/</span>
          <Link href="/categories" className="text-green-600 hover:text-green-800">
            Danh mục
          </Link>
          <span className="mx-2 text-gray-400">/</span>
          <span className="text-gray-600">{categoryInfo.name}</span>
        </nav>

        {/* Filters */}
        <div className="bg-white p-6 rounded-lg shadow-sm mb-8">
          <div className="grid md:grid-cols-3 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder={`Tìm kiếm trong ${categoryInfo.name.toLowerCase()}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <div className="flex gap-4">
              <Select value={priceFilter} onValueChange={setPriceFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Lọc theo giá" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tất cả giá</SelectItem>
                  <SelectItem value="under50k">Dưới 50.000đ</SelectItem>
                  <SelectItem value="50k-100k">50.000đ - 100.000đ</SelectItem>
                  <SelectItem value="over100k">Trên 100.000đ</SelectItem>
                </SelectContent>
              </Select>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger>
                  <SelectValue placeholder="Sắp xếp theo" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="name">Tên A-Z</SelectItem>
                  <SelectItem value="price-low">Giá thấp đến cao</SelectItem>
                  <SelectItem value="price-high">Giá cao đến thấp</SelectItem>
                  <SelectItem value="rating">Đánh giá cao nhất</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center ml-auto text-sm text-gray-600">
              <Filter className="w-4 h-4 mr-2" />
              {filteredAndSortedProducts.length} sản phẩm
            </div>
          </div>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
          {currentProducts.map((product) => (
            <Link key={product.id} href={`/products/${product.id}`}>
              <Card className="hover:shadow-lg transition-shadow cursor-pointer h-full">
                <CardContent className="p-4">
                  <div className="relative mb-3">
                    <Image
                      src={product.image || "/placeholder.svg"}
                      alt={product.name}
                      width={250}
                      height={250}
                      className="rounded-lg object-cover w-full h-48"
                    />
                    {!product.inStock && <Badge className="absolute top-2 left-2 bg-red-500">Hết hàng</Badge>}
                  </div>
                  <h3 className="font-semibold text-green-800 mb-2 line-clamp-2 text-sm">{product.name}</h3>
                  <div className="flex items-center mb-2">
                    <Star className="w-3 h-3 text-yellow-400 fill-current" />
                    <span className="ml-1 text-xs text-gray-600">{product.rating}</span>
                  </div>
                  <p className="text-lg font-bold text-green-600">{product.price.toLocaleString("vi-VN")}đ</p>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-4">
            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.max(prev - 1, 1))}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="w-4 h-4 mr-2" />
              Trước
            </Button>

            <div className="flex space-x-2">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                const pageNum = i + 1
                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    onClick={() => setCurrentPage(pageNum)}
                    className={currentPage === pageNum ? "bg-green-600 hover:bg-green-700" : ""}
                  >
                    {pageNum}
                  </Button>
                )
              })}
            </div>

            <Button
              variant="outline"
              onClick={() => setCurrentPage((prev) => Math.min(prev + 1, totalPages))}
              disabled={currentPage === totalPages}
            >
              Sau
              <ChevronRight className="w-4 h-4 ml-2" />
            </Button>
          </div>
        )}

        {currentProducts.length > 0 && (
          <p className="text-center text-sm text-gray-600 mt-4">
            Trang {currentPage} / {totalPages} - Hiển thị {currentProducts.length} / {filteredAndSortedProducts.length}{" "}
            sản phẩm
          </p>
        )}

        {currentProducts.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">🔍</div>
            <h3 className="text-xl font-semibold text-gray-800 mb-2">Không tìm thấy sản phẩm</h3>
            <p className="text-gray-600 mb-6">Thử thay đổi bộ lọc hoặc từ khóa tìm kiếm</p>
            <Button
              onClick={() => {
                setSearchTerm("")
                setPriceFilter("all")
                setCurrentPage(1)
              }}
              className="bg-green-600 hover:bg-green-700"
            >
              Xóa bộ lọc
            </Button>
          </div>
        )}
      </div>

      {/* Footer */}
      <footer className="bg-green-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h4 className="font-bold mb-4">Anvie</h4>
              <p className="text-green-200 text-sm">
                Cung cấp thực phẩm hữu cơ chất lượng cao, an toàn cho sức khỏe và môi trường.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Kết</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-green-200 hover:text-white">
                    Trang chủ
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-green-200 hover:text-white">
                    Sản phẩm
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-green-200 hover:text-white">
                    Danh mục
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-green-200 hover:text-white">
                    Liên hệ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Danh Mục</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/categories/rau-cu-qua" className="text-green-200 hover:text-white">
                    Rau củ quả
                  </Link>
                </li>
                <li>
                  <Link href="/categories/sua-thuc-vat" className="text-green-200 hover:text-white">
                    Sữa thực vật
                  </Link>
                </li>
                <li>
                  <Link href="/categories/ngu-coc" className="text-green-200 hover:text-white">
                    Ngũ cốc
                  </Link>
                </li>
                <li>
                  <Link href="/categories/dau-hat" className="text-green-200 hover:text-white">
                    Đậu hạt
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Hệ</h4>
              <p className="text-green-200 text-sm mb-2">📧 <EMAIL></p>
              <p className="text-green-200 text-sm mb-2">📞 0123 456 789</p>
              <p className="text-green-200 text-sm">📍 TP. Hồ Chí Minh</p>
            </div>
          </div>
          <div className="border-t border-green-700 mt-8 pt-8 text-center">
            <p className="text-green-200 text-sm">© 2025 Anvie. Tất cả quyền được bảo lưu.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
