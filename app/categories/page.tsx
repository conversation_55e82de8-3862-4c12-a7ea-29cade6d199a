"use client"

import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"
import { ShoppingCartComponent } from "@/components/shopping-cart"

const categories = [
  {
    name: "<PERSON><PERSON> củ quả",
    slug: "rau-cu-qua",
    count: 45,
    icon: "🥬",
    description: "<PERSON>u củ quả tươi ngon, sạch, không hóa chất",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "Sữa thực vật",
    slug: "sua-thuc-vat",
    count: 28,
    icon: "🥛",
    description: "S<PERSON><PERSON> từ hạt, không lactose, gi<PERSON>u dinh dưỡng",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "<PERSON><PERSON> cốc",
    slug: "ngu-coc",
    count: 32,
    icon: "🌾",
    description: "<PERSON><PERSON> cốc nguyên hạt, gi<PERSON><PERSON> chất x<PERSON> và protein",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "<PERSON><PERSON><PERSON> hạ<PERSON>",
    slug: "dau-hat",
    count: 25,
    icon: "🫘",
    description: "<PERSON><PERSON><PERSON> hạt dinh dưỡng, nguồn protein thực vật",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "Thịt, cá, trứng",
    slug: "thit-ca-trung",
    count: 35,
    icon: "🥩",
    description: "Thịt, cá, trứng tươi ngon, chất lượng cao",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "Gia vị",
    slug: "gia-vi",
    count: 18,
    icon: "🌿",
    description: "Gia vị tự nhiên, thảo mộc sạch",
    image: "/placeholder.svg?height=300&width=400",
  },
  {
    name: "Bánh kẹo",
    slug: "banh-keo",
    count: 22,
    icon: "🍪",
    description: "Bánh kẹo ngon miệng, không chất bảo quản",
    image: "/placeholder.svg?height=300&width=400",
  },
]

export default function CategoriesPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <svg width="40" height="40" viewBox="0 0 40 40" className="text-green-600">
                <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
                <path d="M12 20c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8z" fill="currentColor" opacity="0.2" />
                <path d="M20 8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2s2-.9 2-2v-8c0-1.1-.9-2-2-2z" fill="currentColor" />
                <path
                  d="M15 15c-.8-.8-.8-2 0-2.8s2-.8 2.8 0l5.7 5.7c.8.8.8 2 0 2.8s-2 .8-2.8 0L15 15z"
                  fill="currentColor"
                />
                <path
                  d="M25 15c.8-.8.8-2 0-2.8s-2-.8-2.8 0l-5.7 5.7c-.8.8.8 2 0 2.8s2 .8 2.8 0L25 15z"
                  fill="currentColor"
                />
              </svg>
              <div>
                <h1 className="text-xl font-bold text-green-800">Anvie</h1>
                <p className="text-xs text-green-600">Thực phẩm sạch Việt Nam</p>
              </div>
            </Link>

            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-green-700 hover:text-green-900 font-medium">
                Trang chủ
              </Link>
              <Link href="/products" className="text-green-700 hover:text-green-900 font-medium">
                Sản phẩm
              </Link>
              <Link href="/categories" className="text-green-700 hover:text-green-900 font-medium">
                Danh mục
              </Link>
              <Link href="/contact" className="text-green-700 hover:text-green-900 font-medium">
                Liên hệ
              </Link>
            </nav>

            <ShoppingCartComponent />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-green-800 mb-4">Danh Mục Sản Phẩm</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Khám phá các danh mục thực phẩm hữu cơ chất lượng cao của chúng tôi
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category, index) => (
            <Link key={index} href={`/categories/${category.slug}`}>
              <Card className="hover:shadow-xl transition-all duration-300 cursor-pointer group border-green-200 hover:border-green-400">
                <CardContent className="p-0">
                  <div className="relative overflow-hidden rounded-t-lg">
                    <img
                      src={category.image || "/placeholder.svg"}
                      alt={category.name}
                      className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                    />
                    <div className="absolute top-4 left-4">
                      <Badge className="bg-green-600 text-white">{category.count} sản phẩm</Badge>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="flex items-center mb-3">
                      <span className="text-3xl mr-3">{category.icon}</span>
                      <h3 className="text-xl font-bold text-green-800 group-hover:text-green-600 transition-colors">
                        {category.name}
                      </h3>
                    </div>
                    <p className="text-gray-600 text-sm leading-relaxed">{category.description}</p>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-2xl p-12 text-white">
            <h2 className="text-3xl font-bold mb-4">Không Tìm Thấy Danh Mục Bạn Cần?</h2>
            <p className="text-xl mb-8 opacity-90">Xem tất cả sản phẩm của chúng tôi hoặc liên hệ để được tư vấn</p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/products">
                <button className="bg-white text-green-700 hover:bg-gray-100 px-8 py-3 rounded-lg font-semibold transition-colors">
                  Xem Tất Cả Sản Phẩm
                </button>
              </Link>
              <Link href="/contact">
                <button className="border-2 border-white text-white hover:bg-white hover:text-green-700 px-8 py-3 rounded-lg font-semibold transition-colors">
                  Liên Hệ Tư Vấn
                </button>
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-green-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h4 className="font-bold mb-4">Anvie</h4>
              <p className="text-green-200 text-sm">
                Cung cấp thực phẩm sạch chất lượng cao, an toàn cho sức khỏe và môi trường.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Kết</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-green-200 hover:text-white">
                    Trang chủ
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-green-200 hover:text-white">
                    Sản phẩm
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-green-200 hover:text-white">
                    Danh mục
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-green-200 hover:text-white">
                    Liên hệ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Danh Mục</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/categories/rau-cu-qua" className="text-green-200 hover:text-white">
                    Rau củ quả
                  </Link>
                </li>
                <li>
                  <Link href="/categories/sua-thuc-vat" className="text-green-200 hover:text-white">
                    Sữa thực vật
                  </Link>
                </li>
                <li>
                  <Link href="/categories/ngu-coc" className="text-green-200 hover:text-white">
                    Ngũ cốc
                  </Link>
                </li>
                <li>
                  <Link href="/categories/dau-hat" className="text-green-200 hover:text-white">
                    Đậu hạt
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Hệ</h4>
              <p className="text-green-200 text-sm mb-2">📧 <EMAIL></p>
              <p className="text-green-200 text-sm mb-2">📞 0123 456 789</p>
              <p className="text-green-200 text-sm">📍 TP. Hồ Chí Minh</p>
            </div>
          </div>
          <div className="border-t border-green-700 mt-8 pt-8 text-center">
            <p className="text-green-200 text-sm">© 2025 Anvie. Tất cả quyền được bảo lưu.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
