import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Anvie - Thực phẩm sạch Việt Nam",
  description:
    "C<PERSON>a hàng thực phẩm sạch Anvie chuyên cung cấp rau củ quả, sữa thực vật, ngũ cốc, thịt cá trứng và các sản phẩm tự nhiên, an toàn cho sức khỏe.",
  keywords: "anvie, thực phẩm sạch, rau củ quả, sữa thực vật, thịt cá trứng, healthy food, clean food, việt nam",
    generator: 'v0.dev'
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="vi">
      <body className={inter.className}>{children}</body>
    </html>
  )
}
