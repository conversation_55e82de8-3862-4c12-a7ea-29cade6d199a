import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Mail, Phone, MapPin, Clock, GraduationCap } from "lucide-react"
import Link from "next/link"
import Image from "next/image"
import { ShoppingCartComponent } from "@/components/shopping-cart"

const teamMembers = [
  {
    name: "<PERSON><PERSON>",
    studentId: "2288600170",
    role: "Trưởng nhóm - Quản lý sản phẩm",
    email: "<EMAIL>",
    phone: "0123 456 792",
    avatar: "/placeholder.svg?height=150&width=150",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    studentId: "2288600211",
    role: "<PERSON>yên vi<PERSON><PERSON> sóc khách hàng",
    email: "<EMAIL>",
    phone: "0123 456 789",
    avatar: "/placeholder.svg?height=150&width=150",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    studentId: "2288600109",
    role: "Phó nhóm - Marketing & Thiết kế",
    email: "<EMAIL>",
    phone: "0123 456 790",
    avatar: "/placeholder.svg?height=150&width=150",
  },
  {
    name: "Nguyễn Hoàng Thúy Vy",
    studentId: "2288603606",
    role: "Chuyên viên Kỹ thuật",
    email: "<EMAIL>",
    phone: "0123 456 791",
    avatar: "/placeholder.svg?height=150&width=150",
  },
]

export default function ContactPage() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-green-100">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center space-x-2">
              <svg width="40" height="40" viewBox="0 0 40 40" className="text-green-600">
                <circle cx="20" cy="20" r="18" fill="currentColor" opacity="0.1" />
                <path d="M12 20c0-4.4 3.6-8 8-8s8 3.6 8 8-3.6 8-8 8-8-3.6-8-8z" fill="currentColor" opacity="0.2" />
                <path d="M20 8c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2s2-.9 2-2v-8c0-1.1-.9-2-2-2z" fill="currentColor" />
                <path
                  d="M15 15c-.8-.8-.8-2 0-2.8s2-.8 2.8 0l5.7 5.7c.8.8.8 2 0 2.8s-2 .8-2.8 0L15 15z"
                  fill="currentColor"
                />
                <path
                  d="M25 15c.8-.8.8-2 0-2.8s-2-.8-2.8 0l-5.7 5.7c-.8.8.8 2 0 2.8s-2 .8 2.8 0L25 15z"
                  fill="currentColor"
                />
              </svg>
              <div>
                <h1 className="text-xl font-bold text-green-800">Anvie</h1>
                <p className="text-xs text-green-600">Thực phẩm sạch Việt Nam</p>
              </div>
            </Link>

            <nav className="hidden md:flex space-x-6">
              <Link href="/" className="text-green-700 hover:text-green-900 font-medium">
                Trang chủ
              </Link>
              <Link href="/products" className="text-green-700 hover:text-green-900 font-medium">
                Sản phẩm
              </Link>
              <Link href="/categories" className="text-green-700 hover:text-green-900 font-medium">
                Danh mục
              </Link>
              <Link href="/contact" className="text-green-700 hover:text-green-900 font-medium">
                Liên hệ
              </Link>
            </nav>

            <ShoppingCartComponent />
          </div>
        </div>
      </header>

      <div className="container mx-auto px-4 py-12">
        {/* Page Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-green-800 mb-4">Liên Hệ Với Chúng Tôi</h1>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Chúng tôi luôn sẵn sàng hỗ trợ bạn với những sản phẩm hữu cơ chất lượng cao nhất. Hãy liên hệ với đội ngũ
            của chúng tôi!
          </p>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 mb-16">
          {/* Contact Form */}
          <Card>
            <CardHeader>
              <CardTitle className="text-2xl text-green-800">Gửi Tin Nhắn</CardTitle>
            </CardHeader>
            <CardContent>
              <form className="space-y-6">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Họ và tên *</label>
                    <Input placeholder="Nhập họ và tên của bạn" />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email *</label>
                    <Input type="email" placeholder="<EMAIL>" />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Số điện thoại</label>
                  <Input placeholder="0123 456 789" />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Chủ đề</label>
                  <Input placeholder="Chủ đề tin nhắn" />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Nội dung *</label>
                  <Textarea placeholder="Nhập nội dung tin nhắn của bạn..." rows={6} />
                </div>

                <Button className="w-full bg-green-600 hover:bg-green-700">
                  <Mail className="w-4 h-4 mr-2" />
                  Gửi Tin Nhắn
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-2xl text-green-800">Thông Tin Liên Hệ</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <MapPin className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Địa chỉ</h3>
                    <p className="text-gray-600">
                      123 Đường Nguyễn Văn Cừ
                      <br />
                      Quận 5, TP. Hồ Chí Minh
                      <br />
                      Việt Nam
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <Phone className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Điện thoại</h3>
                    <p className="text-gray-600">
                      Hotline: 1900 1234
                      <br />
                      Mobile: 0123 456 789
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <Mail className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Email</h3>
                    <p className="text-gray-600">
                      <EMAIL>
                      <br />
                      <EMAIL>
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-4">
                  <div className="bg-green-100 p-3 rounded-full">
                    <Clock className="w-6 h-6 text-green-600" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 mb-1">Giờ làm việc</h3>
                    <p className="text-gray-600">
                      Thứ 2 - Thứ 6: 8:00 - 18:00
                      <br />
                      Thứ 7 - Chủ nhật: 8:00 - 17:00
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Map placeholder */}
            <Card>
              <CardContent className="p-0">
                <div className="bg-gray-200 h-64 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="w-12 h-12 text-gray-400 mx-auto mb-2" />
                    <p className="text-gray-500">Bản đồ vị trí cửa hàng</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Team Members */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-green-800 mb-4">Đội Ngũ Của Chúng Tôi</h2>
            <p className="text-lg text-gray-600">Gặp gỡ những người đứng sau Anvie</p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="mb-4">
                    <Image
                      src={member.avatar || "/placeholder.svg"}
                      alt={member.name}
                      width={150}
                      height={150}
                      className="rounded-full mx-auto object-cover w-24 h-24"
                    />
                  </div>

                  <h3 className="text-xl font-bold text-green-800 mb-1">{member.name}</h3>

                  <div className="flex items-center justify-center mb-2">
                    <GraduationCap className="w-4 h-4 text-green-600 mr-1" />
                    <span className="text-sm text-gray-600">{member.studentId}</span>
                  </div>

                  <p className="text-green-600 font-medium mb-4">{member.role}</p>

                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center justify-center">
                      <Mail className="w-4 h-4 mr-2" />
                      <span className="truncate">{member.email}</span>
                    </div>
                    <div className="flex items-center justify-center">
                      <Phone className="w-4 h-4 mr-2" />
                      <span>{member.phone}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="bg-gradient-to-r from-green-600 to-green-700 rounded-2xl p-12 text-center text-white">
          <h2 className="text-3xl font-bold mb-4">Sẵn Sàng Mua Sắm?</h2>
          <p className="text-xl mb-8 opacity-90">
            Khám phá bộ sưu tập thực phẩm hữu cơ tươi ngon của chúng tôi ngay hôm nay!
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/products">
              <Button size="lg" className="bg-white text-green-700 hover:bg-gray-100">
                Xem Sản Phẩm
              </Button>
            </Link>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white hover:text-green-700 bg-transparent"
            >
              Tìm Hiểu Thêm
            </Button>
          </div>
        </div>
      </div>

      {/* Footer */}
      <footer className="bg-green-800 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h4 className="font-bold mb-4">Anvie</h4>
              <p className="text-green-200 text-sm">
                Cung cấp thực phẩm hữu cơ chất lượng cao, an toàn cho sức khỏe và môi trường.
              </p>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Kết</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-green-200 hover:text-white">
                    Trang chủ
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-green-200 hover:text-white">
                    Sản phẩm
                  </Link>
                </li>
                <li>
                  <Link href="/categories" className="text-green-200 hover:text-white">
                    Danh mục
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-green-200 hover:text-white">
                    Liên hệ
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Danh Mục</h4>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/categories/rau-cu-qua" className="text-green-200 hover:text-white">
                    Rau củ quả
                  </Link>
                </li>
                <li>
                  <Link href="/categories/sua-thuc-vat" className="text-green-200 hover:text-white">
                    Sữa thực vật
                  </Link>
                </li>
                <li>
                  <Link href="/categories/ngu-coc" className="text-green-200 hover:text-white">
                    Ngũ cốc
                  </Link>
                </li>
                <li>
                  <Link href="/categories/dau-hat" className="text-green-200 hover:text-white">
                    Đậu hạt
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h4 className="font-bold mb-4">Liên Hệ</h4>
              <p className="text-green-200 text-sm mb-2">📧 <EMAIL></p>
              <p className="text-green-200 text-sm mb-2">📞 0123 456 789</p>
              <p className="text-green-200 text-sm">📍 TP. Hồ Chí Minh</p>
            </div>
          </div>
          <div className="border-t border-green-700 mt-8 pt-8 text-center">
            <p className="text-green-200 text-sm">© 2024 Anvie. Tất cả quyền được bảo lưu.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}
