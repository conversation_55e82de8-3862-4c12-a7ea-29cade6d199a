# Hướng dẫn sử dụng ảnh thực tế cho sản phẩm

## 🎯 Giải pháp hiện tại: Unsplash Source API

Hiện tại website đang sử dụng **Unsplash Source API** để tự động lấy ảnh thực tế cho các sản phẩm:

```
https://source.unsplash.com/250x250/?carrot
https://source.unsplash.com/250x250/?almond-milk
https://source.unsplash.com/250x250/?quinoa
```

### ✅ **Ưu điểm:**
- **Miễn phí** và không cần API key
- **Ảnh chất lượng cao** từ Unsplash
- **Tự động** dựa trên tên sản phẩm
- **Không vi phạm bản quyền**
- **Responsive** với nhiều kích thước

### ⚠️ **Hạn chế:**
- Ảnh c<PERSON> thể thay đổi theo thời gian
- Không kiểm soát được ảnh cụ thể
- <PERSON><PERSON> thuộc vào kết nối internet

## 🚀 **Giải pháp nâng cao (Khuyến nghị)**

### **1. Sử dụng Unsplash API chính thức:**

```bash
# Cài đặt
npm install unsplash-js

# Đăng ký tại: https://unsplash.com/developers
# Lấy Access Key miễn phí
```

```javascript
// utils/unsplash.js
import { createApi } from 'unsplash-js';

const unsplash = createApi({
  accessKey: process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY,
});

export const searchProductImage = async (query) => {
  const result = await unsplash.search.getPhotos({
    query,
    page: 1,
    perPage: 1,
    orientation: 'squarish',
  });
  
  return result.response?.results[0]?.urls?.small || null;
};
```

### **2. Tải ảnh về local (Tốt nhất):**

```bash
# Tạo script tự động tải ảnh
node scripts/download-images.js
```

```javascript
// scripts/download-images.js
const fs = require('fs');
const https = require('https');
const path = require('path');

const products = [
  { name: 'Cà rốt', query: 'carrot' },
  { name: 'Sữa hạnh nhân', query: 'almond-milk' },
  // ... thêm sản phẩm
];

const downloadImage = (url, filename) => {
  const file = fs.createWriteStream(`public/images/products/${filename}`);
  https.get(url, (response) => {
    response.pipe(file);
  });
};

// Tải ảnh cho từng sản phẩm
products.forEach(product => {
  const imageUrl = `https://source.unsplash.com/400x400/?${product.query}`;
  downloadImage(imageUrl, `${product.name.toLowerCase().replace(/\s+/g, '-')}.jpg`);
});
```

### **3. Sử dụng Pexels API (Thay thế):**

```bash
npm install pexels
```

```javascript
// utils/pexels.js
import { createClient } from 'pexels';

const client = createClient(process.env.PEXELS_API_KEY);

export const searchProductImage = async (query) => {
  const photos = await client.photos.search({ query, per_page: 1 });
  return photos.photos[0]?.src?.medium || null;
};
```

## 📁 **Cấu trúc thư mục đề xuất:**

```
public/
├── images/
│   ├── products/
│   │   ├── vegetables/
│   │   │   ├── carrot.jpg
│   │   │   ├── cabbage.jpg
│   │   │   └── ...
│   │   ├── plant-milk/
│   │   │   ├── almond-milk.jpg
│   │   │   ├── oat-milk.jpg
│   │   │   └── ...
│   │   ├── grains/
│   │   ├── nuts/
│   │   ├── meat-fish-eggs/
│   │   ├── spices/
│   │   └── snacks/
│   └── categories/
│       ├── vegetables-banner.jpg
│       ├── plant-milk-banner.jpg
│       └── ...
```

## 🔧 **Cách triển khai:**

### **Bước 1: Tạo thư mục**
```bash
mkdir -p public/images/products/{vegetables,plant-milk,grains,nuts,meat-fish-eggs,spices,snacks}
mkdir -p public/images/categories
```

### **Bước 2: Cập nhật helper function**
```javascript
// utils/getProductImage.js
export const getProductImage = (productName, category) => {
  const slug = productName.toLowerCase().replace(/\s+/g, '-');
  const categoryFolder = {
    'Rau củ quả': 'vegetables',
    'Sữa thực vật': 'plant-milk',
    'Ngũ cốc': 'grains',
    'Đậu hạt': 'nuts',
    'Thịt, cá, trứng': 'meat-fish-eggs',
    'Gia vị': 'spices',
    'Bánh kẹo': 'snacks',
  }[category] || 'vegetables';
  
  // Kiểm tra ảnh local trước
  const localImage = `/images/products/${categoryFolder}/${slug}.jpg`;
  
  // Fallback về Unsplash nếu không có ảnh local
  const fallbackImage = `https://source.unsplash.com/250x250/?${getSearchTerm(productName)}`;
  
  return localImage;
};
```

### **Bước 3: Tối ưu hóa**
```javascript
// next.config.js
module.exports = {
  images: {
    domains: ['source.unsplash.com', 'images.unsplash.com'],
    formats: ['image/webp', 'image/avif'],
  },
};
```

## 📊 **So sánh các giải pháp:**

| Giải pháp | Chi phí | Chất lượng | Kiểm soát | Tốc độ |
|-----------|---------|------------|-----------|---------|
| Unsplash Source | Miễn phí | Cao | Thấp | Trung bình |
| Unsplash API | Miễn phí | Cao | Cao | Cao |
| Pexels API | Miễn phí | Cao | Cao | Cao |
| Local Images | Miễn phí | Tùy chỉnh | Hoàn toàn | Rất cao |

## 🎯 **Khuyến nghị:**

1. **Ngắn hạn**: Tiếp tục sử dụng Unsplash Source (đã triển khai)
2. **Trung hạn**: Chuyển sang Unsplash API để kiểm soát tốt hơn
3. **Dài hạn**: Tải ảnh về local để tối ưu hiệu suất

## 🔗 **Tài nguyên hữu ích:**

- [Unsplash API Documentation](https://unsplash.com/documentation)
- [Pexels API Documentation](https://www.pexels.com/api/)
- [Next.js Image Optimization](https://nextjs.org/docs/basic-features/image-optimization)
- [Free Stock Photo Sites](https://www.freepik.com/)
